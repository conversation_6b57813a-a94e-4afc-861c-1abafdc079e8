import 'package:carousel_slider/carousel_slider.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/sm_chat/bloc/sm_chat_bloc.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/chat_list_model.dart';
import 'package:flowkar/features/sm_chat/presentation/flowkar_chat_screen.dart';
import 'package:flowkar/features/sm_chat/presentation/sm_chat_detail_screen.dart';
import 'package:flowkar/features/sm_chat/presentation/telegram_chat_detail_screen.dart';
import 'package:flowkar/features/sm_chat/widget/flowkar_search_user_list.dart';
import 'package:flowkar/features/sm_chat/widget/sm_chat_list_screen_shimmer.dart';

import 'package:flowkar/features/widgets/custom/custom_conditional_scroll_physics.dart';
import 'package:flutter/cupertino.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';
import 'package:flowkar/core/socket/socket_service.dart';

int _selectedCategoryKey = 0;

class SmChatScreen extends StatefulWidget {
  const SmChatScreen({super.key});

  static Widget builder(BuildContext context) {
    return SmChatScreen();
  }

  @override
  State<SmChatScreen> createState() => _SmChatScreenState();
}

class _SmChatScreenState extends State<SmChatScreen> with SingleTickerProviderStateMixin {
  final CarouselSliderController _carouselController = CarouselSliderController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  int _currentIndex = 0;
  String selectedCategory = "Flowkar";
  bool _isInitialized = false;

  // Add filtered lists for local search
  List<dynamic> _filteredChatItems = [];
  bool _isLocalSearchActive = false;

  // Typing indicator functionality
  Map<int, bool> typingUsers = {}; // Map to track which users are typing

  List<Map<String, dynamic>> categories = [
    {'label': 'Flowkar', 'icon': Assets.images.icons.other.icFlowkar.path, 'isSelected': true},
    {'label': 'All (Meta)', 'icon': Assets.images.icons.social.svgMeta.path},
    {'label': 'Facebook', 'icon': Assets.images.icons.social.facebook.path},
    {'label': 'Instagram', 'icon': Assets.images.icons.social.insta.path},
    {'label': 'Telegram', 'icon': Assets.images.icons.social.svgTelegram.path},
  ];

  @override
  void initState() {
    super.initState();
    // Always set isMessageScreen to false when entering chat list screen
    isMessageScreen = false;
    Logger.lOG("SM_CHAT_SCREEN: initState - Set isMessageScreen to false");

    _loadSavedCategory();
    // Set up socket listeners only once when screen is initialized
    _setupTypingIndicatorListener();
  }

  Future<void> _loadSavedCategory() async {
    try {
      final int savedIndex = _selectedCategoryKey;

      setState(() {
        _currentIndex = savedIndex;
        selectedCategory = categories[savedIndex]['label'];

        for (int i = 0; i < categories.length; i++) {
          categories[i]['isSelected'] = (i == savedIndex);
        }

        _isInitialized = true;
      });

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_carouselController.ready) {
          _carouselController.jumpToPage(savedIndex);
        }
      });

      _loadCategoryData();
    } catch (e) {
      _resetToFlowkar();
    }
  }

  Future<void> _saveSelectedCategory(int index) async {
    try {
      _selectedCategoryKey = index;
    } catch (e) {
      Logger.lOG("Error saving category preference: $e");
    }
  }

  void _loadCategoryData() {
    if (selectedCategory == 'Flowkar') {
      context.read<SmChatBloc>().add(const GetChatListEvent(page: 1, isReload: false));
    } else {
      context.read<SmChatBloc>().add(GetSmchatListEvent());
      context.read<SmChatBloc>().add(GetTelegramUerListAPI());
    }
  }

  void _setupTypingIndicatorListener() {
    Logger.lOG("SM_CHAT_SCREEN: Setting up socket listeners");

    // Set up typing indicator listener for Flowkar platform only
    SocketService.response(
      APIConfig.isTyping,
      (response) {
        if (mounted && selectedCategory == 'Flowkar') {
          final userId = response['user_id'];
          final isTyping = response['is_typing'] ?? false;

          Logger.lOG(
              "SM_CHAT_SCREEN: Typing indicator - UserId: $userId, IsTyping: $isTyping, isMessageScreen: $isMessageScreen");

          // Only update chat list if we're not in an individual chat screen
          if (!isMessageScreen) {
            setState(() {
              if (isTyping) {
                typingUsers[userId] = true;
              } else {
                typingUsers.remove(userId);
              }
            });
          }
        }
      },
    );

    // Set up receive message listener for updating chat list
    SocketService.response(
      APIConfig.receivemessage,
      (response) {
        if (mounted && selectedCategory == 'Flowkar') {
          final senderId = response['sent_by'];
          final message = response['message'];
          final createdAt = response['created_at'];
          final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID);

          Logger.lOG(
              "SM_CHAT_SCREEN: Received message - SentBy: $senderId, Message: $message, isMessageScreen: $isMessageScreen");

          // Only update chat list if we're not in an individual chat screen and message is not from current user
          if (!isMessageScreen && senderId != currentUserId) {
            // Clear typing indicator for this user
            setState(() {
              typingUsers.remove(senderId);
            });

            _updateChatListWithNewMessage(senderId, message, createdAt);
          }
        }
      },
    );
  }

  void _updateChatListWithNewMessage(int senderId, String message, String createdAt) {
    final currentState = context.read<SmChatBloc>().state;
    final chatList = List<FlowkarChatData>.from(currentState.chatList);

    // Find if chat with this user already exists
    int existingChatIndex = chatList.indexWhere((chat) => chat.userId == senderId);

    if (existingChatIndex != -1) {
      // Update existing chat
      final existingChat = chatList[existingChatIndex];
      final updatedChat = FlowkarChatData(
        userId: existingChat.userId,
        touser: existingChat.touser,
        profileImage: existingChat.profileImage,
        userName: existingChat.userName,
        name: existingChat.name,
        latestMessage: message,
        createdAt: createdAt,
        isRead: false, // Mark as unread
      );

      // Remove from current position and add to top
      chatList.removeAt(existingChatIndex);
      chatList.insert(0, updatedChat);
    } else {
      // For new chat, we would need user details from API
      // This is a simplified version - in real implementation you might need to fetch user details
      final newChat = FlowkarChatData(
        userId: senderId,
        touser: Prefobj.preferences?.get(Prefkeys.USER_ID),
        profileImage: '',
        userName: 'User $senderId',
        name: 'User $senderId',
        latestMessage: message,
        createdAt: createdAt,
        isRead: false,
      );
      chatList.insert(0, newChat);
    }

    // Update the state
    context.read<SmChatBloc>().add(UpdateChatListEvent(chatList: chatList));
  }

  void _resetToFlowkar() {
    setState(() {
      _currentIndex = 0;
      selectedCategory = "Flowkar";

      for (int i = 0; i < categories.length; i++) {
        categories[i]['isSelected'] = (i == 0);
      }

      _isInitialized = true;
    });

    _saveSelectedCategory(0);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_carouselController.ready) {
        _carouselController.jumpToPage(0);
      }
    });

    context.read<SmChatBloc>().add(const GetChatListEvent(page: 1, isReload: false));
  }

  // Add local search functionality
  // void _performLocalSearch(String query, SmChatState state, SmChatState flowkarChatState) {
  //   if (query.isEmpty) {
  //     setState(() {
  //       _isLocalSearchActive = false;
  //       _filteredChatItems = [];
  //     });
  //     return;
  //   }

  //   List<dynamic> allChatItems = [];

  //   if (selectedCategory == 'Flowkar') {
  //     // For Flowkar, we'll still use the existing search functionality
  //     setState(() {
  //       _isLocalSearchActive = false;
  //     });
  //     return;
  //   } else if (selectedCategory == 'All (Meta)') {
  //     // Search in both Facebook and Instagram
  //     for (var platform in platforms.keys) {
  //       final items = _getItemsForPlatform(state, platform);
  //       allChatItems.addAll(items.map((item) => {'item': item, 'platform': platform}));
  //     }
  //   } else {
  //     // Search in specific platform
  //     final platformId = selectedCategory.toLowerCase();
  //     if (platforms.containsKey(platformId)) {
  //       final items = _getItemsForPlatform(state, platformId);
  //       allChatItems.addAll(items.map((item) => {'item': item, 'platform': platformId}));
  //     }
  //   }

  //   // Filter based on search query
  //   final filteredItems = allChatItems.where((chatItem) {
  //     final item = chatItem['item'];
  //     final name = (item.name ?? '').toLowerCase();
  //     return name.contains(query.toLowerCase());
  //   }).toList();

  //   setState(() {
  //     _isLocalSearchActive = true;
  //     _filteredChatItems = filteredItems;
  //   });
  // }
  void _performLocalSearch(String query, SmChatState state, SmChatState flowkarChatState) {
    if (query.isEmpty) {
      setState(() {
        _isLocalSearchActive = false;
        _filteredChatItems = [];
      });
      return;
    }

    List<dynamic> allChatItems = [];

    if (selectedCategory == 'Flowkar') {
      // For Flowkar, we'll still use the existing search functionality
      setState(() {
        _isLocalSearchActive = false;
      });
      return;
    } else if (selectedCategory == 'All (Meta)') {
      // Search in both Facebook and Instagram (excluding Telegram from Meta)
      for (var platform in platforms.keys) {
        final items = _getItemsForPlatform(state, platform);
        allChatItems.addAll(items.map((item) => {'item': item, 'platform': platform}));
      }
    } else if (selectedCategory == 'Telegram') {
      // Add Telegram search functionality
      final items = _getItemsForPlatform(state, 'telegram');
      allChatItems.addAll(items.map((item) => {'item': item, 'platform': 'telegram'}));
    } else {
      // Search in specific platform
      final platformId = selectedCategory.toLowerCase();
      if (platforms.containsKey(platformId)) {
        final items = _getItemsForPlatform(state, platformId);
        allChatItems.addAll(items.map((item) => {'item': item, 'platform': platformId}));
      }
    }

    // Filter based on search query
    final filteredItems = allChatItems.where((chatItem) {
      final item = chatItem['item'];
      final name = (item.name ?? '').toLowerCase();
      return name.contains(query.toLowerCase());
    }).toList();

    setState(() {
      _isLocalSearchActive = true;
      _filteredChatItems = filteredItems;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Always ensure isMessageScreen is false when this screen is active
    if (isMessageScreen) {
      isMessageScreen = false;
      Logger.lOG("SM_CHAT_SCREEN: didChangeDependencies - Set isMessageScreen to false");
    }

    if (!_isInitialized) {
      _resetToFlowkar();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Ensure isMessageScreen is always false when this screen is being built
    isMessageScreen = false;
    selectedCategory = categories[_currentIndex]['label'];
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppbar(
        hasLeadingIcon: false,
        alignment: Alignment.center,
        title: Lang.of(context).lbl_messages,
        textStyle: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
      ),
      body: BlocListener<ConnectivityBloc, ConnectivityState>(
        listener: (context, state) {
          if (state.isReconnected) {
            _loadCategoryData();
          }
        },
        child: LiquidPullToRefresh(
          color: Theme.of(context).primaryColor.withOpacity(0.5),
          showChildOpacityTransition: false,
          backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
          onRefresh: () async {
            _loadCategoryData();
            return Future.value();
          },
          child: BlocBuilder<SmChatBloc, SmChatState>(
            builder: (context, smChatState) {
              final flowkarChatState = context.watch<SmChatBloc>().state;

              final hasSearchResults = flowkarChatState.searchuserList.isNotEmpty;

              return BlocBuilder<ConnectivityBloc, ConnectivityState>(
                builder: (context, connectivityState) {
                  if (!connectivityState.isConnected &&
                      smChatState.singleChatModel == null &&
                      smChatState.smchatListModel == null &&
                      smChatState.telegramSendMassageModel == null) {
                    return SmChatListShimmer();
                  } else if (smChatState.isloding) {
                    return SmChatListShimmer();
                  } else {
                    return Column(
                      children: [
                        _buildHeader(smChatState, flowkarChatState),
                        buildSizedBoxH(8),
                        _buildSearchTextField(context, smChatState, flowkarChatState),
                        if (selectedCategory != 'Flowkar' && selectedCategory != 'Telegram') ...[
                          buildSizedBoxH(16),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.0.w),
                            child: Text(
                              "Note: Messaging on Instagram via Flowkar is allowed only within 24 hours of the last user interaction.",
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontSize: 12.sp,
                                    color: ThemeData().customColors.black.withOpacity(0.5),
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ],
                        buildSizedBoxH(20),

                        // _buildTabs(),
                        // if (_showMarkOptions) _buildMarkOptions(),
                        Expanded(child: _buildMessageList(smChatState, flowkarChatState, hasSearchResults, context)),
                      ],
                    );
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }

  int getCategoryCount(SmChatState smState, SmChatState chatState, String categoryLabel) {
    switch (categoryLabel) {
      case 'Flowkar':
        return chatState.chatList.length;

      case 'All (Meta)':
        int facebookCount = smState.smchatListModel?.data?.facebook?.length ?? 0;
        int instagramCount = smState.smchatListModel?.data?.instagram?.length ?? 0;
        return (facebookCount + instagramCount);

      case 'Facebook':
        return smState.smchatListModel?.data?.facebook?.length ?? 0;

      case 'Instagram':
        return smState.smchatListModel?.data?.instagram?.length ?? 0;

      case 'Telegram':
        return smState.telegramUserListModel?.data.length ?? 0;

      default:
        return 0;
    }
  }

  Widget _buildHeader(SmChatState state, SmChatState chatState) {
    return Container(
      padding: EdgeInsets.only(bottom: 8.0.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_left),
            onPressed: _currentIndex > 0
                ? () {
                    _carouselController.previousPage();
                    setState(() {
                      _currentIndex--;
                      for (int i = 0; i < categories.length; i++) {
                        categories[i]['isSelected'] = (i == _currentIndex);
                      }
                    });
                    _saveSelectedCategory(_currentIndex);
                    // Clear search when changing category
                    _searchController.clear();
                    _isLocalSearchActive = false;
                    _filteredChatItems = [];
                    // _loadCategoryData();
                  }
                : null,
            color: _currentIndex > 0 ? null : Theme.of(context).customColors.disableButtonColor,
          ),
          Expanded(
            child: StatefulBuilder(
              builder: (BuildContext context, void Function(void Function()) setState) {
                return CarouselSlider(
                  carouselController: _carouselController,
                  options: CarouselOptions(
                    disableCenter: true,
                    height: 70,
                    viewportFraction: 0.3,
                    initialPage: _currentIndex,
                    enableInfiniteScroll: false,
                    enlargeCenterPage: true,
                    onPageChanged: (index, reason) {
                      setState(() {
                        _currentIndex = index;
                        for (int i = 0; i < categories.length; i++) {
                          categories[i]['isSelected'] = (i == index);
                        }
                      });

                      this.setState(() {
                        selectedCategory = categories[index]['label'];
                        // Clear search when changing category
                        _searchController.clear();
                        _isLocalSearchActive = false;
                        _filteredChatItems = [];
                      });

                      _saveSelectedCategory(index);

                      // _loadCategoryData();
                    },
                  ),
                  items: categories.asMap().entries.map((entry) {
                    int index = entry.key;
                    var category = entry.value;
                    int count = getCategoryCount(state, chatState, category['label']);

                    return GestureDetector(
                      onTap: () {
                        _carouselController.animateToPage(index);
                        setState(() {
                          _currentIndex = index;
                          for (int i = 0; i < categories.length; i++) {
                            categories[i]['isSelected'] = (i == index);
                          }
                        });

                        // Update parent state
                        this.setState(() {
                          selectedCategory = categories[index]['label'];
                          // Clear search when changing category
                          _searchController.clear();
                          _isLocalSearchActive = false;
                          _filteredChatItems = [];
                        });

                        // Save the selected category
                        _saveSelectedCategory(index);

                        // Load appropriate data
                        // _loadCategoryData();
                      },
                      child: _buildCategoryIcon(
                        category['label'],
                        category['icon'],
                        count: count,
                        isSelected: category['isSelected'] ?? false,
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ),
          IconButton(
            icon: Icon(Icons.chevron_right),
            onPressed: _currentIndex < categories.length - 1
                ? () {
                    _carouselController.nextPage();
                    setState(() {
                      _currentIndex++;
                      for (int i = 0; i < categories.length; i++) {
                        categories[i]['isSelected'] = (i == _currentIndex);
                      }
                    });
                    _saveSelectedCategory(_currentIndex);
                    // Clear search when changing category
                    _searchController.clear();
                    _isLocalSearchActive = false;
                    _filteredChatItems = [];
                    // _loadCategoryData();
                  }
                : null,
            color: _currentIndex < categories.length - 1 ? null : Theme.of(context).customColors.disableButtonColor,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryIcon(String label, String icon, {bool isSelected = false, int count = 0}) {
    return Column(
      children: [
        Stack(
          children: [
            CustomImageView(
              imagePath: icon,
              height: isSelected ? 45.h : 35.h,
            ),
            // if (count > 0)
            //   Positioned(
            //     right: 0,
            //     top: 0,
            //     child: Container(
            //       padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            //       decoration: BoxDecoration(
            //         color: Colors.red,
            //         borderRadius: BorderRadius.circular(10),
            //       ),
            //       child: Text(
            //         count.toString(),
            //         style: TextStyle(
            //           color: Colors.white,
            //           fontSize: 10,
            //           fontWeight: FontWeight.bold,
            //         ),
            //       ),
            //     ),
            //   ),
          ],
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Theme.of(context).primaryColor : Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchTextField(BuildContext context, SmChatState smChatState, SmChatState flowkarChatState) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0.w),
      child: ValueListenableBuilder<TextEditingValue>(
        valueListenable: _searchController,
        builder: (context, value, child) {
          return FlowkarTextFormField(
            hint: Lang.of(context).lbl_search,
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 14.sp,
                  color: ThemeData().customColors.black.withOpacity(0.5),
                  fontWeight: FontWeight.w500,
                ),
            context: context,
            controller: _searchController,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.icSearch,
              height: 20.0.h,
              margin: EdgeInsets.all(16.0),
            ),
            onChanged: (value) {
              if (selectedCategory == 'Flowkar') {
                // Use existing search for Flowkar
                context.read<SmChatBloc>().add(SearchUserListEvent(searchtext: value));
              } else {
                // Use local search for other categories
                _performLocalSearch(value, smChatState, flowkarChatState);
              }
            },
            fillColor: ThemeData().customColors.fillcolor,
            borderDecoration: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10.r)),
              borderSide: BorderSide.none,
            ),
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
            suffixIcon: value.text.isNotEmpty
                ? CustomImageView(
                    imagePath: AssetConstants.icClose,
                    height: 15.0.h,
                    margin: EdgeInsets.all(16.0),
                    onTap: () {
                      _searchController.clear();
                      FocusManager.instance.primaryFocus?.unfocus();
                      setState(() {
                        _isLocalSearchActive = false;
                        _filteredChatItems = [];
                      });
                    },
                  )
                : null,
          );
        },
      ),
    );
  }

  Widget _buildDismissBackground() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(4.0.r),
      ),
      child: Align(
        alignment: Alignment.centerRight,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomImageView(
                imagePath: Assets.images.svg.setting.svgDeleteAccount.path,
                height: 20.0.h,
              ),
              buildSizedBoxW(10),
              Text("Delete"),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMessageList(
      SmChatState state, SmChatState flowkarChatState, bool hasSearchResults, BuildContext context) {
    List<dynamic> chatItems = [];
    final isSearchActive = _searchController.text.isNotEmpty;

    // Use filtered items if local search is active, otherwise use regular logic
    if (_isLocalSearchActive && selectedCategory != 'Flowkar') {
      chatItems = _filteredChatItems;
    } else if (selectedCategory == 'Flowkar') {
      // Add Flowkar chats from SmChatBloc
      chatItems.addAll(flowkarChatState.chatList.map((chat) => {'item': chat, 'platform': 'Flowkar'}));
    } else if (selectedCategory == 'All (Meta)') {
      for (var platform in platforms.keys) {
        final items = _getItemsForPlatform(state, platform);
        chatItems.addAll(items.map((item) => {'item': item, 'platform': platform}));
      }
    } else if (selectedCategory == 'Telegram') {
      final items = _getItemsForPlatform(state, 'telegram');
      chatItems.addAll(items.map((item) => {'item': item, 'platform': 'telegram'}));
    } else {
      final platformId = selectedCategory.toLowerCase();
      if (platforms.containsKey(platformId)) {
        final items = _getItemsForPlatform(state, platformId);
        chatItems.addAll(items.map((item) => {'item': item, 'platform': platformId}));
      }
    }

    return BlocBuilder<ConnectivityBloc, ConnectivityState>(
      builder: (context, connectivityState) {
        if (!connectivityState.isConnected && chatItems.isEmpty) {
          return ChatListShimmer();
        } else if (state.isloding) {
          return ChatListShimmer();
        } else {
          if (selectedCategory == 'Flowkar' && isSearchActive) {
            return flowkarChatState.searchuserListLoading == true
                ? ChatListShimmer()
                : hasSearchResults
                    ? FlowkarSearchUserListWidget(
                        searchcontroller: _searchController,
                        userList: flowkarChatState.searchuserList,
                        onclose: () {
                          final state = context.read<SmChatBloc>().state;
                          if (state.chatList.isNotEmpty) {
                            state.chatList.clear();
                          }
                          context.read<SmChatBloc>().add(const GetChatListEvent(page: 1, isReload: false));
                        },
                      )
                    : FutureBuilder(
                        future: Future.delayed(Duration(seconds: 2)),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState == ConnectionState.waiting) {
                            return ChatListShimmer();
                          } else {
                            return _buildNoSearchListFound();
                          }
                        },
                      );
          }

          if (_isLocalSearchActive && selectedCategory != 'Flowkar' && chatItems.isEmpty) {
            return _buildNoSearchListFound();
          }

          // Show empty state or chat list
          //MARK : SearchList
          return chatItems.isEmpty
              ? FutureBuilder(
                  future: Future.delayed(Duration(seconds: 2)),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState != ConnectionState.done) {
                      return ChatListShimmer();
                    }

                    return ListView(
                      physics: AlwaysScrollableScrollPhysics(),
                      children: [
                        buildSizedBoxH(MediaQuery.of(context).size.height / 8),
                        ExceptionWidget(
                          imagePath: Assets.images.svg.exception.svgNodatafound.path,
                          showButton: false,
                          title: Lang.of(context).lbl_no_data_found,
                          subtitle: Lang.of(context).lbl_no_message,
                        ),
                      ],
                    );
                  },
                )
              : ListView.separated(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom + 80.h),
                  shrinkWrap: true,
                  controller: _scrollController,
                  physics: ConditionalAlwaysScrollPhysics(controller: _scrollController),
                  itemCount: chatItems.length,
                  itemBuilder: (ctx, index) {
                    final chatItem = chatItems[index];
                    final item = chatItem['item'];
                    final platform = chatItem['platform'];

                    return platform == "Flowkar"
                        ? Dismissible(
                            key: Key(chatItems[index]['item'].userId.toString()),
                            // direction: DismissDirection.endToStart,
                            direction: platform != 'Flowkar' ? DismissDirection.none : DismissDirection.endToStart,
                            background: _buildDismissBackground(),

                            confirmDismiss: (direction) {
                              return showDialog(
                                context: context,
                                builder: (ctx) {
                                  FocusScope.of(context).unfocus();
                                  bool isLoading = false;
                                  return StatefulBuilder(builder: (ctx, setState) {
                                    return CustomAlertDialog(
                                        imagePath: AssetConstants.icDelete,
                                        title: "Delete This Chat",
                                        subtitle: Lang.of(context).lbl_delete_chat,
                                        onConfirmButtonPressed: () async {
                                          setState(() {
                                            isLoading = true;
                                          });

                                          final userId = chatItems[index]['item'].userId ?? 0;
                                          setState(() {
                                            chatItems.removeAt(index);
                                          });

                                          context.read<SmChatBloc>().add(DeleteChatApiEvent(userId: userId));
                                        },
                                        confirmButtonText: Lang.of(ctx).lbl_delete,
                                        isLoading: isLoading);
                                  });
                                },
                              );
                            },
                            child: _buildMessageItem(
                              platform == 'telegram' ? item.name ?? '' : item.name ?? '',
                              platform == 'telegram'
                                  ? item.lastMessage?.text ?? 'Message not available'
                                  : item.latestMessage ?? 'Message not available',
                              isUnread: false,
                              hasMessage: false,
                              platform: platform,
                              item: item,
                              ctx: context,
                            ),
                          )
                        : _buildMessageItem(
                            platform == 'telegram' ? item.name ?? '' : item.name ?? '',
                            platform == 'telegram'
                                ? item.lastMessage?.text ?? 'Message not available'
                                : item.latestMessage ?? 'Message not available',
                            isUnread: false,
                            hasMessage: false,
                            platform: platform,
                            item: item,
                            ctx: context,
                          );
                  },
                  separatorBuilder: (BuildContext context, int index) {
                    return buildSizedBoxH(10);
                  },
                );
        }
      },
    );
  }

  Widget _buildNoSearchListFound() {
    return Center(
        child: ExceptionWidget(
      imagePath: AssetConstants.pngNoResultFound,
      title: Lang.of(context).lbl_no_result_found,
      subtitle: Lang.of(context).lbl_no_result_found_subtitle,
      showButton: false,
    ));
  }

  List<dynamic> _getItemsForPlatform(SmChatState state, String platform) {
    switch (platform) {
      case 'facebook':
        return state.smchatListModel?.data?.facebook ?? [];
      case 'instagram':
        return state.smchatListModel?.data?.instagram ?? [];
      case 'telegram':
        return state.telegramUserListModel?.data ?? [];
      default:
        return [];
    }
  }

  Widget _buildMessageItem(String name, String message,
      {bool isUnread = false, String platform = '', bool hasMessage = false, dynamic item, required BuildContext ctx}) {
    // Determine platform info
    final platformInfo = platform == 'Flowkar'
        ? SocialPlatform(
            id: 'Flowkar',
            name: 'Flowkar',
            icon: Assets.images.icons.other.icFlowkar.path,
            color: Colors.grey,
          )
        : platform == 'telegram'
            ? SocialPlatform(
                id: 'telegram',
                name: 'telegram',
                icon: Assets.images.icons.social.svgTelegram.path,
                color: Colors.grey,
              )
            : platforms[platform] ??
                SocialPlatform(
                  id: 'unknown',
                  name: 'Unknown',
                  icon: Assets.images.pngs.other.pngLogo.path,
                  color: Colors.grey,
                );

    // Use correct data based on platform
    final profileImage = platform == 'Flowkar' ? item.profileImage : '';

    return InkResponse(
      onTap: () {
        if (platform == "Flowkar") {
          FocusScope.of(context).unfocus();
          final state = context.read<SmChatBloc>().state;

          PersistentNavBarNavigator.pushNewScreen(
            ctx,
            screen: FlowkarChatScreen(
              isDeepLink: false,
              args: {
                'item': item,
                'callback': () {
                  if (state.chatList.isNotEmpty) {
                    state.chatList.clear();
                  }
                  context.read<SmChatBloc>().add(const GetChatListEvent(page: 1, isReload: false));
                }
              },
            ),
            withNavBar: false,
            customPageRoute: CustomCupertinoPageRoute(
              builder: (context) => FlowkarChatScreen(
                isDeepLink: false,
                args: {
                  'item': item,
                  'callback': () {
                    if (state.chatList.isNotEmpty) {
                      state.chatList.clear();
                    }
                    context.read<SmChatBloc>().add(const GetChatListEvent(page: 1, isReload: false));
                  }
                },
              ),
            ),
          );

          massageUserID = item.userId ?? 0;
          touser = item.touser ?? 0;
        } else if (platform == "telegram") {
          // Handle Telegram chat navigation
          //args[0]-->name
          //args[1]-->platformInfo
          //args[2]-->targetUserId
          //args[3]-->ownerId (optional for telegram)
          final state = context.read<SmChatBloc>().state;
          if (state.telegramUserChatListModel?.data.isNotEmpty ?? false) {
            state.telegramUserChatListModel?.data.clear();
          }
          FocusScope.of(context).unfocus();
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: TelegramChatDetailScreen(
              args: [
                name, // args[0] - name (from DialogData)
                platformInfo, // args[1] - platformInfo
                item.id.toString(), // args[2] - targetUserId (DialogData.id as string)
                null,
                item.type.toString()
              ],
            ),
            withNavBar: false,
          );
        } else {
          FocusScope.of(context).unfocus();
          PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: SmChatDetailScreen(
              args: [
                name,
                platformInfo,
                item.conversationId,
                item.ownerId,
                item.chattingUserId,
              ],
            ),
            withNavBar: false,
          );
        }
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 22.0.w),
        padding: EdgeInsets.only(left: 14.0.w, right: 32.0.w, top: 14.0.h, bottom: 14.0.h),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.08),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Row(
          children: [
            Stack(
              children: [
                CustomImageView(
                  imagePath: platform == 'Flowkar'
                      ? (profileImage?.isNotEmpty ?? false
                          ? profileImage.startsWith('/media/')
                              ? APIConfig.mainbaseURL + profileImage
                              : profileImage
                          : AssetConstants.pngUserReomve)
                      : AssetConstants.pngUserReomve,
                  fit: BoxFit.cover,
                  height: 50.h,
                  width: 50.w,
                  radius: BorderRadius.circular(100.r),
                ),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: CustomImageView(
                    imagePath: platformInfo.icon,
                    height: 15.h,
                    width: 15.h,
                    radius: BorderRadius.circular(100.r),
                  ),
                ),
              ],
            ),
            buildSizedBoxW(18),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontSize: 14.78.sp,
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                  buildSizedBoxH(8),
                  // Show typing indicator for Flowkar platform if user is typing
                  platform == 'Flowkar' && typingUsers.containsKey(item.userId) && typingUsers[item.userId] == true
                      ? Text(
                          "typing...",
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.w500,
                            fontStyle: FontStyle.italic,
                          ),
                        )
                      // Row(
                      //     children: [
                      //       SizedBox(
                      //         height: 20.h,
                      //         child: TypingIndicator(
                      //           showIndicator: true,
                      //           userrname: '',
                      //           bubblecolor: Theme.of(context).primaryColor,
                      //         ),
                      //       ),
                      //       buildSizedBoxW(8),
                      //       Text(
                      //         "typing...",
                      //         style: TextStyle(
                      //           fontSize: 13.sp,
                      //           color: Theme.of(context).primaryColor,
                      //           fontWeight: FontWeight.w500,
                      //           fontStyle: FontStyle.italic,
                      //         ),
                      //       ),
                      //     ],
                      //   )
                      : Text(
                          maxLines: 1,
                          message.isNotEmpty ? message : "Message not available",
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: Theme.of(context).customColors.subHeadingcolor,
                            fontWeight: FontWeight.w700,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                ],
              ),
            ),
            buildSizedBoxW(18),
            if (platform == 'Flowkar' ? !item.isRead : hasMessage)
              Icon(
                Icons.circle,
                size: 15.sp,
                color: Theme.of(context).primaryColor,
              )
          ],
        ),
      ),
    );
  }
}

class SocialPlatform {
  final String id;
  final String name;
  final String icon;
  final Color color;

  const SocialPlatform({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}

final Map<String, SocialPlatform> platforms = {
  'facebook': SocialPlatform(
    id: 'facebook',
    name: 'Facebook',
    icon: Assets.images.icons.social.facebook.path,
    color: Colors.blue,
  ),
  'instagram': SocialPlatform(
    id: 'instagram',
    name: 'Instagram',
    icon: Assets.images.icons.social.insta.path,
    color: Colors.pink,
  ),
};

class CustomCupertinoPageRoute extends CupertinoPageRoute {
  CustomCupertinoPageRoute({
    required WidgetBuilder builder,
    RouteSettings? settings,
    bool maintainState = true,
    bool fullscreenDialog = false,
  }) : super(
          builder: builder,
          settings: settings,
          maintainState: maintainState,
          fullscreenDialog: fullscreenDialog,
        );

  @override
  bool get popGestureEnabled => true;
}
